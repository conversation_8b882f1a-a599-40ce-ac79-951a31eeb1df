require("dotenv").config();
const express = require("express");
const nodemailer = require("nodemailer");
const cors = require("cors");
const path = require("path");

const app = express();
const port = 3000;

// Middlewares
app.use(cors());
app.use(express.json());

// Serve static files from public
app.use(express.static(path.join(__dirname, "../public")));

// Contact form endpoint
app.post("/send-email", async (req, res) => {
  try {
    const { name, email, subject, message } = req.body;
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: process.env.EMAIL_USER,
      subject: `رسالة جديدة من ${name}: ${subject}`,
      text: `اسم المرسل: ${name}\nالبريد الإلكتروني: ${email}\n\nالرسالة:\n${message}`,
    };
    await transporter.sendMail(mailOptions);
    res.status(200).json({ message: "تم إرسال الرسالة بنجاح" });
  } catch (error) {
    console.error("Error sending email:", error);
    res.status(500).json({ message: "حدث خطأ أثناء إرسال الرسالة" });
  }
});

// Start server
app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
});
