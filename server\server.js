require("dotenv").config();
const express = require("express");
const nodemailer = require("nodemailer");
const cors = require("cors");
const path = require("path");
const multer = require("multer");
const fs = require("fs");
const helmet = require("helmet");
const rateLimit = require("express-rate-limit");
const { body, validationResult } = require("express-validator");

const app = express();
const port = 3000;

// إنشاء مجلد للملفات المرفوعة إذا لم يكن موجوداً
const uploadsDir = path.join(__dirname, "uploads");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// إعداد multer لرفع الملفات
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    // إنشاء اسم فريد للملف
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const fileExtension = path.extname(file.originalname);
    cb(null, file.fieldname + "-" + uniqueSuffix + fileExtension);
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB حد أقصى لكل ملف
    files: 5, // حد أقصى 5 ملفات
  },
  fileFilter: function (req, file, cb) {
    // السماح بأنواع ملفات محددة فقط
    const allowedTypes = /jpeg|jpg|png|gif|pdf|doc|docx|txt/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(
        new Error(
          "نوع الملف غير مسموح. الأنواع المسموحة: JPEG, JPG, PNG, GIF, PDF, DOC, DOCX, TXT"
        )
      );
    }
  },
});

// إعدادات الأمان
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: [
          "'self'",
          "'unsafe-inline'",
          "https://fonts.googleapis.com",
          "https://cdnjs.cloudflare.com",
        ],
        fontSrc: [
          "'self'",
          "https://fonts.gstatic.com",
          "https://cdnjs.cloudflare.com",
        ],
        scriptSrc: ["'self'", "'unsafe-inline'"],
        imgSrc: ["'self'", "data:", "blob:"],
        connectSrc: ["'self'"],
      },
    },
  })
);

// تحديد معدل الطلبات العام
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 دقيقة
  max: 200, // حد أقصى 200 طلب لكل IP (زيادة للمرونة)
  message: {
    message: "تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// تحديد معدل خاص لنموذج التواصل (قابل للتخصيص)
const contactRateLimit = parseInt(process.env.CONTACT_RATE_LIMIT) || 20;
const contactWindowHours = parseInt(process.env.CONTACT_WINDOW_HOURS) || 1;

const contactLimiter = rateLimit({
  windowMs: contactWindowHours * 60 * 60 * 1000, // قابل للتخصيص من .env
  max: contactRateLimit, // قابل للتخصيص من .env
  message: {
    message: `تم تجاوز الحد المسموح من الرسائل (${contactRateLimit} رسالة/${
      contactWindowHours === 1 ? "ساعة" : contactWindowHours + " ساعات"
    }). يرجى المحاولة لاحقاً.`,
  },
  // إعدادات إضافية للمرونة
  standardHeaders: true,
  legacyHeaders: false,
  // السماح بتجاوز الحد للطلبات المحلية (للاختبار والتطوير)
  skip: (req) => {
    const isDevelopment = process.env.NODE_ENV === "development";
    const isLocalhost =
      req.ip === "127.0.0.1" ||
      req.ip === "::1" ||
      req.ip === "::ffff:127.0.0.1";
    return isDevelopment && isLocalhost;
  },
});

// Middlewares
app.use(limiter);
app.use(
  cors({
    origin: process.env.NODE_ENV === "production" ? false : true,
    credentials: true,
  })
);
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Serve static files from public
app.use(express.static(path.join(__dirname, "../public")));

// قواعد التحقق من صحة البيانات
const validateContactForm = [
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("الاسم يجب أن يكون بين 2 و 100 حرف")
    .matches(/^[\u0600-\u06FFa-zA-Z\s]+$/)
    .withMessage("الاسم يجب أن يحتوي على أحرف عربية أو إنجليزية فقط"),

  body("email")
    .trim()
    .isEmail()
    .withMessage("يرجى إدخال بريد إلكتروني صحيح")
    .normalizeEmail(),

  body("subject")
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage("الموضوع يجب ألا يتجاوز 200 حرف"),

  body("message")
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage("الرسالة يجب أن تكون بين 10 و 2000 حرف"),
];

// Contact form endpoint with file upload support
app.post(
  "/send-email",
  contactLimiter,
  upload.array("attachments", 5),
  validateContactForm,
  async (req, res) => {
    try {
      // فحص أخطاء التحقق من صحة البيانات
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          message: "بيانات غير صحيحة",
          errors: errors.array().map((err) => err.msg),
        });
      }

      // التحقق من صحة البيانات
      const { name, email, subject, message } = req.body;

      // إعداد النقل
      const transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: process.env.SMTP_PORT,
        secure: false,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS,
        },
      });

      // إعداد المرفقات
      const attachments = [];
      if (req.files && req.files.length > 0) {
        req.files.forEach((file) => {
          attachments.push({
            filename: file.originalname,
            path: file.path,
          });
        });
      }

      // إعداد خيارات البريد
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: process.env.EMAIL_USER,
        subject: `رسالة جديدة من ${name}: ${
          subject || "رسالة من موقع جميل علقم"
        }`,
        html: `
        <div dir="rtl" style="font-family: Arial, sans-serif;">
          <h2>رسالة جديدة من الموقع</h2>
          <hr>
          <p><strong>اسم المرسل:</strong> ${name}</p>
          <p><strong>البريد الإلكتروني:</strong> ${email}</p>
          <p><strong>الموضوع:</strong> ${
            subject || "رسالة من موقع جميل علقم"
          }</p>
          <hr>
          <h3>نص الرسالة:</h3>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px;">
            ${message.replace(/\n/g, "<br>")}
          </div>
          ${
            attachments.length > 0
              ? `
          <hr>
          <h3>المرفقات:</h3>
          <ul>
            ${attachments.map((att) => `<li>${att.filename}</li>`).join("")}
          </ul>
          `
              : ""
          }
        </div>
      `,
        attachments: attachments,
      };

      // إرسال البريد
      await transporter.sendMail(mailOptions);

      // حذف الملفات المؤقتة بعد الإرسال
      if (req.files && req.files.length > 0) {
        req.files.forEach((file) => {
          fs.unlink(file.path, (err) => {
            if (err) console.error("خطأ في حذف الملف:", err);
          });
        });
      }

      res.status(200).json({ message: "تم إرسال الرسالة بنجاح" });
    } catch (error) {
      console.error("Error sending email:", error);

      // حذف الملفات في حالة الخطأ
      if (req.files && req.files.length > 0) {
        req.files.forEach((file) => {
          fs.unlink(file.path, (err) => {
            if (err) console.error("خطأ في حذف الملف:", err);
          });
        });
      }

      res.status(500).json({ message: "حدث خطأ أثناء إرسال الرسالة" });
    }
  }
);

// معالجة أخطاء multer
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === "LIMIT_FILE_SIZE") {
      return res
        .status(400)
        .json({ message: "حجم الملف كبير جداً. الحد الأقصى 10MB" });
    }
    if (error.code === "LIMIT_FILE_COUNT") {
      return res
        .status(400)
        .json({ message: "عدد الملفات كبير جداً. الحد الأقصى 5 ملفات" });
    }
  }

  if (error.message.includes("نوع الملف غير مسموح")) {
    return res.status(400).json({ message: error.message });
  }

  res.status(500).json({ message: "حدث خطأ في الخادم" });
});

// Start server
app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
});
