# موقع المهندس جميل علقم الشخصي

موقع شخصي احترافي للمهندس جميل أحمد عبدالرحيم علقم، مهندس ميكانيكي ومدرب مع خبرة 18 سنة في إدارة المصانع والإنتاج.

## 🌟 المميزات

- **تصميم متجاوب** يعمل على جميع الأجهزة
- **دعم كامل للغة العربية** مع اتجاه RTL
- **نموذج تواصل متقدم** مع إمكانية رفع الملفات
- **معرض صور تفاعلي** للدورات والشهادات
- **أمان عالي** مع حماية من الهجمات
- **تحقق من صحة البيانات** شامل
- **معالجة أخطاء متقدمة**

## 🏗️ البنية التقنية

### الواجهة الأمامية (Frontend)
- **HTML5** مع دعم RTL
- **CSS3** مع تأثيرات حركية
- **JavaScript** للتفاعل
- **Font Awesome** للأيقونات
- **Google Fonts** للخطوط العربية

### الخادم (Backend)
- **Node.js** مع Express.js
- **Nodemailer** لإرسال البريد الإلكتروني
- **Multer** لرفع الملفات
- **Helmet** للأمان
- **Express Rate Limit** للحماية من الهجمات
- **Express Validator** للتحقق من البيانات

## 📁 هيكل المشروع

```
Personal/
├── public/                 # الواجهة الأمامية
│   ├── index.html         # الصفحة الرئيسية
│   ├── cv.html           # السيرة التدريبية
│   ├── services.html     # الخدمات والدورات
│   ├── certificates.html # الشهادات
│   ├── contact.html      # التواصل
│   ├── images/           # الصور
│   └── js/               # ملفات JavaScript
├── server/                # الخادم
│   ├── server.js         # الملف الرئيسي للخادم
│   ├── package.json      # تبعيات Node.js
│   ├── .env             # متغيرات البيئة
│   └── uploads/         # مجلد الملفات المرفوعة
└── README.md            # هذا الملف
```

## 🚀 التشغيل

### المتطلبات
- Node.js (الإصدار 14 أو أحدث)
- npm أو yarn

### خطوات التشغيل

1. **تثبيت التبعيات:**
```bash
cd server
npm install
```

2. **إعداد متغيرات البيئة:**
قم بتحديث ملف `.env` في مجلد `server`:
```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
```

3. **تشغيل الخادم:**
```bash
npm start
```

4. **فتح المتصفح:**
انتقل إلى `http://localhost:3000`

## 📧 إعداد البريد الإلكتروني

### Gmail
1. فعّل المصادقة الثنائية
2. أنشئ App Password
3. استخدم App Password في `.env`

### Outlook/Hotmail
```env
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
```

### Yahoo
```env
SMTP_HOST=smtp.mail.yahoo.com
SMTP_PORT=587
```

## 🔒 الأمان

- **Helmet.js** للحماية من هجمات XSS و CSRF
- **Rate Limiting** لمنع الهجمات DDoS
- **تحقق من صحة البيانات** شامل
- **تشفير الملفات** المرفوعة
- **تنظيف البيانات** تلقائياً

## 📱 الصفحات

1. **الرئيسية** - نبذة عن المهندس جميل
2. **السيرة التدريبية** - الدورات المقدمة مع الصور
3. **الخدمات والدورات** - الخدمات المتاحة
4. **الشهادات** - الشهادات المهنية والعلمية
5. **التواصل** - نموذج تواصل مع رفع ملفات

## 🛠️ التطوير

### إضافة صفحة جديدة
1. أنشئ ملف HTML في مجلد `public`
2. أضف الروابط في شريط التنقل
3. أضف الأنماط المطلوبة

### تخصيص التصميم
- الألوان الرئيسية في متغيرات CSS
- الخطوط في بداية ملفات CSS
- الأيقونات من Font Awesome

## 📞 التواصل

- **الموقع:** http://localhost:3000
- **البريد الإلكتروني:** <EMAIL>
- **الواتساب:** +962796843499

## 📄 الترخيص

جميع الحقوق محفوظة © 2024 جميل علقم

---

تم تطوير هذا المشروع بواسطة الذكاء الاصطناعي لخدمة المهندس جميل علقم.
